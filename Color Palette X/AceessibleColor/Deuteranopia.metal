#include <metal_stdlib>
using namespace metal;

[[stitchable]]                      // SwiftUI 可以使用这个函数作为 shader
half4 deuteranopia(float2 position,    // 像素位置（未使用，可忽略）
                 half4 color)        // 当前像素的 RGBA 值，half4 是 4 个 16 位浮点
{
    // 颜色矩阵运算（线性组合 RGB）
    half r = color.r * 0.625 + color.g * 0.375 + color.b*0.000;
    half g = color.r * 0.700 + color.g * 0.300 + color.b*0.000;
    half b = color.r * 0.000 + color.g * 0.300 + color.b*0.700;

    return half4(r, g, b, color.a); // 返回变换后的 RGBA
}
