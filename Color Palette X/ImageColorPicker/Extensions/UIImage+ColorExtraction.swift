//
//  UIImage+ColorExtraction.swift
//  Color Palette X
//
//  Created by <PERSON><PERSON> on 2025/7/27.
//

import CoreImage
import UIKit

/// UIImage 颜色提取扩展
///
/// 提供从图片中提取主要颜色的功能，使用改进的 K-Means 聚类算法
/// 支持 LAB 颜色空间、颜色过滤和透明像素处理
extension UIImage {

    /// 颜色提取错误类型
    enum ColorExtractionError: Error, LocalizedError {
        case invalidImage
        case imageProcessingFailed
        case pixelDataExtractionFailed
        case noValidPixels
        case noColorsExtracted
        case invalidParameters

        var errorDescription: String? {
            switch self {
            case .invalidImage:
                return NSLocalizedString("invalid_image", comment: "无效的图片")
            case .imageProcessingFailed:
                return NSLocalizedString("image_processing_failed", comment: "图片处理失败")
            case .pixelDataExtractionFailed:
                return NSLocalizedString("pixel_data_extraction_failed", comment: "像素数据提取失败")
            case .noValidPixels:
                return NSLocalizedString("no_valid_pixels", comment: "没有有效的像素数据")
            case .noColorsExtracted:
                return NSLocalizedString("no_colors_extracted", comment: "未提取到颜色")
            case .invalidParameters:
                return NSLocalizedString("invalid_parameters", comment: "无效的参数")
            }
        }
    }

    /// LAB 颜色空间表示
    private struct LABColor {
        let l: Double  // 亮度 (0-100)
        let a: Double  // 绿-红轴 (-128 to 127)
        let b: Double  // 蓝-黄轴 (-128 to 127)

        /// 计算与另一个 LAB 颜色的距离
        func distance(to other: LABColor) -> Double {
            let deltaL = l - other.l
            let deltaA = a - other.a
            let deltaB = b - other.b
            return sqrt(deltaL * deltaL + deltaA * deltaA + deltaB * deltaB)
        }
    }

    /// RGB 颜色空间表示
    private struct RGBColor {
        let r: Double  // 红色分量 (0-1)
        let g: Double  // 绿色分量 (0-1)
        let b: Double  // 蓝色分量 (0-1)
        let weight: Double  // 权重 (像素数量)

        /// 转换为 LAB 颜色空间
        func toLAB() -> LABColor {
            // 首先转换为 XYZ 颜色空间
            let xyz = toXYZ()

            // 然后转换为 LAB
            return xyz.toLAB()
        }

        /// 转换为 XYZ 颜色空间
        private func toXYZ() -> XYZColor {
            // 应用 gamma 校正
            let rLinear = gammaCorrection(r)
            let gLinear = gammaCorrection(g)
            let bLinear = gammaCorrection(b)

            // 转换为 XYZ (使用 sRGB 矩阵)
            let x = rLinear * 0.4124564 + gLinear * 0.3575761 + bLinear * 0.1804375
            let y = rLinear * 0.2126729 + gLinear * 0.7151522 + bLinear * 0.0721750
            let z = rLinear * 0.0193339 + gLinear * 0.1191920 + bLinear * 0.9503041

            return XYZColor(x: x, y: y, z: z)
        }

        /// Gamma 校正函数
        private func gammaCorrection(_ value: Double) -> Double {
            if value > 0.04045 {
                return pow((value + 0.055) / 1.055, 2.4)
            } else {
                return value / 12.92
            }
        }

        /// 计算颜色的饱和度 (HSV)
        func saturation() -> Double {
            let maxVal = max(r, max(g, b))
            let minVal = min(r, min(g, b))

            if maxVal == 0 {
                return 0
            }

            return (maxVal - minVal) / maxVal
        }

        /// 计算颜色的亮度 (HSV)
        func brightness() -> Double {
            return max(r, max(g, b))
        }

        /// 判断是否为有效的颜色（过滤黑色、灰色、低饱和度颜色）
        func isValidColor() -> Bool {
            let saturation = saturation()
            let brightness = brightness()

            // 过滤条件：
            // 1. 饱和度太低（灰色）
            // 2. 亮度太低（黑色）
            // 3. 亮度太高且饱和度低（白色/浅灰色）
            if saturation < 0.15 && brightness < 0.8 {
                return false  // 过滤灰色和深色
            }

            if brightness < 0.1 {
                return false  // 过滤纯黑色
            }

            if brightness > 0.95 && saturation < 0.1 {
                return false  // 过滤纯白色
            }

            return true
        }
    }

    /// XYZ 颜色空间表示
    private struct XYZColor {
        let x: Double
        let y: Double
        let z: Double

        /// 转换为 LAB 颜色空间
        func toLAB() -> LABColor {
            // 使用 D65 白点
            let xn = 0.95047
            let yn = 1.00000
            let zn = 1.08883

            let fx = labFunction(x / xn)
            let fy = labFunction(y / yn)
            let fz = labFunction(z / zn)

            let l = 116.0 * fy - 16.0
            let a = 500.0 * (fx - fy)
            let b = 200.0 * (fy - fz)

            return LABColor(l: l, a: a, b: b)
        }

        /// LAB 转换函数
        private func labFunction(_ t: Double) -> Double {
            let delta = 6.0 / 29.0
            if t > delta * delta * delta {
                return pow(t, 1.0 / 3.0)
            } else {
                return t / (3.0 * delta * delta) + 4.0 / 29.0
            }
        }
    }
    
    /// 从图片中提取指定数量的主颜色
    /// - Parameters:
    ///   - count: 要提取的主颜色数量 (默认值为 8)
    ///   - iterations: K-Means 算法的迭代次数 (默认值为 10)
    ///   - perceptual: 是否使用感知色彩空间进行计算 (默认值为 true，使用 LAB 颜色空间)
    /// - Returns: 一个包含 ExtractedColor 对象数组，按其在图像中的权重降序排列
    /// - Throws: ColorExtractionError 如果提取过程失败
    func extractDominantColors(
        count: Int = 8,
        iterations: Int = 10,
        perceptual: Bool = true
    ) throws -> [ExtractedColor] {

        AppLogger.info("开始改进的颜色提取 - count: \(count), iterations: \(iterations), perceptual: \(perceptual)", category: .performance)

        // 验证参数
        guard count > 0 && count <= 32 else {
            AppLogger.error("无效的颜色数量: \(count)", category: .data)
            throw ColorExtractionError.invalidParameters
        }

        guard iterations > 0 && iterations <= 50 else {
            AppLogger.error("无效的迭代次数: \(iterations)", category: .data)
            throw ColorExtractionError.invalidParameters
        }

        // 预处理图片并提取像素数据
        let processedImage = preprocessImage()
        let pixelData = try extractPixelData(from: processedImage)

        AppLogger.debug("提取到 \(pixelData.count) 个有效像素", category: .performance)

        // 过滤有效颜色（排除黑色、灰色、透明像素）
        let validColors = pixelData.filter { $0.isValidColor() }

        guard !validColors.isEmpty else {
            AppLogger.warning("没有找到有效的颜色像素", category: .data)
            throw ColorExtractionError.noValidPixels
        }

        AppLogger.debug("过滤后剩余 \(validColors.count) 个有效颜色像素", category: .performance)

        // 执行 K-Means 聚类
        let clusters = try performKMeansClustering(
            colors: validColors,
            clusterCount: count,
            iterations: iterations,
            usePerceptualSpace: perceptual
        )

        // 转换为 ExtractedColor 对象
        let extractedColors = clusters.map { cluster in
            ExtractedColor(
                red: cluster.r,
                green: cluster.g,
                blue: cluster.b,
                weight: cluster.weight
            )
        }.sorted { $0.weight > $1.weight }

        AppLogger.info("成功提取 \(extractedColors.count) 个主要颜色", category: .performance)

        return extractedColors
    }
    
    // MARK: - 私有方法

    /// 预处理图片以优化性能
    /// - Returns: 处理后的图片
    private func preprocessImage() -> UIImage {
        let maxDimension: CGFloat = 100  // 减小尺寸以提高性能

        // 如果图片尺寸过大，进行缩放
        if size.width > maxDimension || size.height > maxDimension {
            let scale = min(maxDimension / size.width, maxDimension / size.height)
            let newSize = CGSize(width: size.width * scale, height: size.height * scale)

            AppLogger.debug("图片尺寸过大，缩放至 \(newSize)", category: .performance)

            return resized(to: newSize) ?? self
        }

        return self
    }

    /// 从图片中提取像素数据
    /// - Parameter image: 要处理的图片
    /// - Returns: RGB 颜色数组
    /// - Throws: ColorExtractionError
    private func extractPixelData(from image: UIImage) throws -> [RGBColor] {
        guard let cgImage = image.cgImage else {
            AppLogger.error("无法获取 CGImage", category: .data)
            throw ColorExtractionError.imageProcessingFailed
        }

        let width = cgImage.width
        let height = cgImage.height
        let bytesPerPixel = 4
        let bytesPerRow = width * bytesPerPixel
        let totalBytes = height * bytesPerRow

        var pixelData = Data(count: totalBytes)

        let success = pixelData.withUnsafeMutableBytes { bytes in
            guard let context = CGContext(
                data: bytes.baseAddress,
                width: width,
                height: height,
                bitsPerComponent: 8,
                bytesPerRow: bytesPerRow,
                space: CGColorSpaceCreateDeviceRGB(),
                bitmapInfo: CGImageAlphaInfo.premultipliedLast.rawValue
            ) else {
                return false
            }

            context.draw(cgImage, in: CGRect(x: 0, y: 0, width: width, height: height))
            return true
        }

        guard success else {
            AppLogger.error("像素数据提取失败", category: .data)
            throw ColorExtractionError.pixelDataExtractionFailed
        }

        // 解析像素数据
        var colors: [RGBColor] = []
        let sampleStride = max(1, width * height / 10000)  // 采样以提高性能

        for i in stride(from: 0, to: totalBytes, by: bytesPerPixel * sampleStride) {
            let r = Double(pixelData[i]) / 255.0
            let g = Double(pixelData[i + 1]) / 255.0
            let b = Double(pixelData[i + 2]) / 255.0
            let alpha = Double(pixelData[i + 3]) / 255.0

            // 过滤透明像素
            if alpha > 0.5 {
                colors.append(RGBColor(r: r, g: g, b: b, weight: 1.0))
            }
        }

        return colors
    }
    
    /// 执行 K-Means 聚类算法
    /// - Parameters:
    ///   - colors: 输入的颜色数组
    ///   - clusterCount: 聚类数量
    ///   - iterations: 最大迭代次数
    ///   - usePerceptualSpace: 是否使用感知色彩空间 (LAB)
    /// - Returns: 聚类中心颜色数组
    /// - Throws: ColorExtractionError
    private func performKMeansClustering(
        colors: [RGBColor],
        clusterCount: Int,
        iterations: Int,
        usePerceptualSpace: Bool
    ) throws -> [RGBColor] {

        guard colors.count >= clusterCount else {
            AppLogger.warning("颜色数量不足以进行聚类", category: .data)
            throw ColorExtractionError.noValidPixels
        }

        // 初始化聚类中心（使用最远点采样方法）
        var centroids = initializeCentroids(from: colors, count: clusterCount, usePerceptualSpace: usePerceptualSpace)

        AppLogger.debug("初始化 \(centroids.count) 个聚类中心", category: .performance)

        // var previousCentroids: [RGBColor] = []  // 暂时不使用收敛检测

        // K-Means 迭代
        for iteration in 0..<iterations {
            // 分配每个颜色到最近的聚类中心
            var clusters: [[RGBColor]] = Array(repeating: [], count: clusterCount)

            for color in colors {
                let nearestCentroidIndex = findNearestCentroid(
                    color: color,
                    centroids: centroids,
                    usePerceptualSpace: usePerceptualSpace
                )
                clusters[nearestCentroidIndex].append(color)
            }

            // 更新聚类中心
            var newCentroids: [RGBColor] = []
            for cluster in clusters {
                if cluster.isEmpty {
                    // 如果聚类为空，保持原来的中心
                    let index = newCentroids.count
                    if index < centroids.count {
                        newCentroids.append(centroids[index])
                    }
                } else {
                    let centroid = calculateCentroid(cluster: cluster)
                    newCentroids.append(centroid)
                }
            }

            // 检查收敛性
            if centroidsConverged(previous: centroids, current: newCentroids) {
                AppLogger.debug("K-Means 在第 \(iteration + 1) 次迭代后收敛", category: .performance)
                break
            }

            // previousCentroids = centroids  // 暂时不使用收敛检测
            centroids = newCentroids
        }

        // 计算每个聚类的权重
        var finalCentroids: [RGBColor] = []
        for (index, centroid) in centroids.enumerated() {
            let clusterColors = colors.filter { color in
                findNearestCentroid(color: color, centroids: centroids, usePerceptualSpace: usePerceptualSpace) == index
            }

            let weight = Double(clusterColors.count) / Double(colors.count)
            let weightedCentroid = RGBColor(r: centroid.r, g: centroid.g, b: centroid.b, weight: weight)
            finalCentroids.append(weightedCentroid)
        }

        return finalCentroids.filter { $0.weight > 0.01 }  // 过滤权重过低的聚类
    }
    
    /// 初始化聚类中心（使用最远点采样方法）
    /// - Parameters:
    ///   - colors: 颜色数组
    ///   - count: 聚类数量
    ///   - usePerceptualSpace: 是否使用感知色彩空间
    /// - Returns: 初始聚类中心
    private func initializeCentroids(from colors: [RGBColor], count: Int, usePerceptualSpace: Bool) -> [RGBColor] {
        guard !colors.isEmpty else { return [] }

        var centroids: [RGBColor] = []

        // 选择第一个中心（随机选择）
        centroids.append(colors.randomElement()!)

        // 使用最远点采样选择其余中心
        for _ in 1..<count {
            var maxDistance = 0.0
            var farthestColor = colors[0]

            for color in colors {
                let minDistanceToCentroids = centroids.map { centroid in
                    colorDistance(color1: color, color2: centroid, usePerceptualSpace: usePerceptualSpace)
                }.min() ?? 0.0

                if minDistanceToCentroids > maxDistance {
                    maxDistance = minDistanceToCentroids
                    farthestColor = color
                }
            }

            centroids.append(farthestColor)
        }

        return centroids
    }

    /// 找到最近的聚类中心
    /// - Parameters:
    ///   - color: 目标颜色
    ///   - centroids: 聚类中心数组
    ///   - usePerceptualSpace: 是否使用感知色彩空间
    /// - Returns: 最近聚类中心的索引
    private func findNearestCentroid(color: RGBColor, centroids: [RGBColor], usePerceptualSpace: Bool) -> Int {
        var minDistance = Double.infinity
        var nearestIndex = 0

        for (index, centroid) in centroids.enumerated() {
            let distance = colorDistance(color1: color, color2: centroid, usePerceptualSpace: usePerceptualSpace)
            if distance < minDistance {
                minDistance = distance
                nearestIndex = index
            }
        }

        return nearestIndex
    }

    /// 计算两个颜色之间的距离
    /// - Parameters:
    ///   - color1: 第一个颜色
    ///   - color2: 第二个颜色
    ///   - usePerceptualSpace: 是否使用感知色彩空间 (LAB)
    /// - Returns: 颜色距离
    private func colorDistance(color1: RGBColor, color2: RGBColor, usePerceptualSpace: Bool) -> Double {
        if usePerceptualSpace {
            // 使用 LAB 颜色空间计算感知距离
            let lab1 = color1.toLAB()
            let lab2 = color2.toLAB()
            return lab1.distance(to: lab2)
        } else {
            // 使用欧几里得距离在 RGB 空间
            let deltaR = color1.r - color2.r
            let deltaG = color1.g - color2.g
            let deltaB = color1.b - color2.b
            return sqrt(deltaR * deltaR + deltaG * deltaG + deltaB * deltaB)
        }
    }

    /// 计算聚类的中心点
    /// - Parameter cluster: 聚类中的颜色数组
    /// - Returns: 聚类中心颜色
    private func calculateCentroid(cluster: [RGBColor]) -> RGBColor {
        guard !cluster.isEmpty else {
            return RGBColor(r: 0, g: 0, b: 0, weight: 0)
        }

        let totalR = cluster.reduce(0.0) { $0 + $1.r }
        let totalG = cluster.reduce(0.0) { $0 + $1.g }
        let totalB = cluster.reduce(0.0) { $0 + $1.b }

        let count = Double(cluster.count)
        return RGBColor(
            r: totalR / count,
            g: totalG / count,
            b: totalB / count,
            weight: 1.0
        )
    }

    /// 检查聚类中心是否收敛
    /// - Parameters:
    ///   - previous: 上一次的聚类中心
    ///   - current: 当前的聚类中心
    /// - Returns: 是否收敛
    private func centroidsConverged(previous: [RGBColor], current: [RGBColor]) -> Bool {
        guard previous.count == current.count else { return false }

        let threshold = 0.01  // 收敛阈值

        for (prev, curr) in zip(previous, current) {
            let distance = colorDistance(color1: prev, color2: curr, usePerceptualSpace: false)
            if distance > threshold {
                return false
            }
        }

        return true
    }

    /// 调整图片尺寸
    /// - Parameter size: 目标尺寸
    /// - Returns: 调整后的图片
    private func resized(to size: CGSize) -> UIImage? {
        UIGraphicsBeginImageContextWithOptions(size, false, scale)
        defer { UIGraphicsEndImageContext() }

        draw(in: CGRect(origin: .zero, size: size))
        return UIGraphicsGetImageFromCurrentImageContext()
    }
}



// MARK: - 预览和测试支持

#if DEBUG
extension UIImage {
    /// 创建测试用的彩色图片
    /// 包含红、绿、蓝、黄四种鲜艳颜色，用于验证颜色提取算法
    static func createTestColorImage(size: CGSize = CGSize(width: 100, height: 100)) -> UIImage? {
        UIGraphicsBeginImageContextWithOptions(size, false, 0)
        defer { UIGraphicsEndImageContext() }

        let context = UIGraphicsGetCurrentContext()

        // 创建包含鲜艳颜色的测试图片
        let colors = [
            UIColor.red,        // 纯红色
            UIColor.green,      // 纯绿色
            UIColor.blue,       // 纯蓝色
            UIColor.yellow      // 纯黄色
        ]
        let sectionWidth = size.width / CGFloat(colors.count)

        for (index, color) in colors.enumerated() {
            color.setFill()
            let rect = CGRect(
                x: CGFloat(index) * sectionWidth,
                y: 0,
                width: sectionWidth,
                height: size.height
            )
            context?.fill(rect)
        }

        return UIGraphicsGetImageFromCurrentImageContext()
    }

    /// 创建包含灰色和黑色的测试图片（用于验证过滤功能）
    static func createTestGrayscaleImage(size: CGSize = CGSize(width: 100, height: 100)) -> UIImage? {
        UIGraphicsBeginImageContextWithOptions(size, false, 0)
        defer { UIGraphicsEndImageContext() }

        let context = UIGraphicsGetCurrentContext()

        // 创建包含灰色调的测试图片
        let colors = [
            UIColor.black,                              // 纯黑色
            UIColor.gray,                               // 灰色
            UIColor.lightGray,                          // 浅灰色
            UIColor(red: 0.8, green: 0.2, blue: 0.2, alpha: 1.0)  // 一个鲜艳的红色
        ]
        let sectionWidth = size.width / CGFloat(colors.count)

        for (index, color) in colors.enumerated() {
            color.setFill()
            let rect = CGRect(
                x: CGFloat(index) * sectionWidth,
                y: 0,
                width: sectionWidth,
                height: size.height
            )
            context?.fill(rect)
        }

        return UIGraphicsGetImageFromCurrentImageContext()
    }

    /// 测试颜色提取功能
    static func testColorExtraction() {
        AppLogger.info("开始测试改进的颜色提取算法", category: .performance)

        // 测试鲜艳颜色图片
        if let testImage = createTestColorImage() {
            do {
                let extractedColors = try testImage.extractDominantColors(count: 4, iterations: 10, perceptual: true)
                AppLogger.info("从测试图片中提取到 \(extractedColors.count) 个颜色:", category: .performance)
                for (index, color) in extractedColors.enumerated() {
                    AppLogger.info("颜色 \(index + 1): \(color.hexString), 权重: \(String(format: "%.2f", color.weight))", category: .performance)
                }
            } catch {
                AppLogger.error("颜色提取测试失败: \(error.localizedDescription)", category: .data)
            }
        }

        // 测试灰色图片（验证过滤功能）
        if let grayscaleImage = createTestGrayscaleImage() {
            do {
                let extractedColors = try grayscaleImage.extractDominantColors(count: 4, iterations: 10, perceptual: true)
                AppLogger.info("从灰色测试图片中提取到 \(extractedColors.count) 个颜色（应该过滤掉大部分灰色）:", category: .performance)
                for (index, color) in extractedColors.enumerated() {
                    AppLogger.info("颜色 \(index + 1): \(color.hexString), 权重: \(String(format: "%.2f", color.weight))", category: .performance)
                }
            } catch {
                AppLogger.error("灰色图片颜色提取测试失败: \(error.localizedDescription)", category: .data)
            }
        }
    }
}
#endif