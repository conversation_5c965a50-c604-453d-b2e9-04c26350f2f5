# 色盲模拟器设计文档

## 概述

色盲模拟器是一个用于检查无障碍色彩可视性的 iOS 应用功能，采用 SwiftUI 和 MVVM 架构模式。该功能通过 Metal 着色器技术实现红色盲（Protanopia）和绿色盲（Deuteranopia）的视觉模拟，帮助设计师和开发者创建更具包容性的色彩方案。

## 架构

### 整体架构模式

采用 MVVM（Model-View-ViewModel）架构模式：

- **Model**: 色卡数据模型，包含颜色信息和状态
- **View**: SwiftUI 视图组件，负责 UI 渲染和用户交互
- **ViewModel**: 业务逻辑处理，使用 `@Observable` 宏进行状态管理

### 导航架构

使用 `NavigationStack` 实现界面导航：
- 从 `HomeView` 的无障碍检查器卡片导航到色盲模拟器主界面
- 支持返回导航和深层链接

### 文件组织结构

```
Color Palette X/
├── ColorBlindnessSimulator/
│   ├── Models/
│   │   ├── ColorCard.swift
│   │   └── ColorBlindnessType.swift
│   ├── ViewModels/
│   │   └── ColorBlindnessSimulatorViewModel.swift
│   ├── Views/
│   │   ├── ColorBlindnessSimulatorView.swift
│   │   ├── ColorCardView.swift
│   │   ├── ColorCardRowView.swift
│   │   └── AddColorCardSheet.swift
│   └── Services/
│       └── ColorBlindnessService.swift
├── Utils/
│   └── ColorConverter.swift
```

## 组件和接口

### 数据模型

#### ColorCard
```swift
@Observable
class ColorCard: Identifiable {
    let id = UUID()
    var backgroundColor: Color
    var textColor: Color
    var hexValue: String { get }
    
    init(backgroundColor: Color, textColor: Color = .white)
}
```

#### ColorBlindnessType
```swift
enum ColorBlindnessType: String, CaseIterable {
    case protanopia = "protanopia"
    case deuteranopia = "deuteranopia"
    
    var displayName: LocalizedStringKey { get }
    var shaderFunction: String { get }
}
```

### 视图模型

#### ColorBlindnessSimulatorViewModel
```swift
@Observable
class ColorBlindnessSimulatorViewModel {
    // MARK: - 状态属性
    var colorCards: [ColorCard] = []
    var isAddCardSheetPresented = false
    var showHelpSheet = false
    var scrollPosition: CGFloat = 0
    
    // MARK: - 初始化
    init()
    
    // MARK: - 公共方法
    func addColorCard(backgroundColor: Color, textColor: Color)
    func removeColorCard(at index: Int)
    func copyColorValue(_ color: Color)
    func updateScrollPosition(_ position: CGFloat)
    func showHelp()
    
    // MARK: - 私有方法
    private func setupDefaultCards()
    private func showCopySuccessMessage()
}
```

### 视图组件

#### ColorBlindnessSimulatorView
主界面视图，包含：
- 导航栏（返回按钮、标题、帮助按钮）
- 三个同步滚动的色卡行
- 弹窗管理

#### ColorCardView
单个色卡视图组件：
- 背景色显示
- "Hi" 文字显示
- 十六进制颜色值显示
- 操作按钮（调色盘、文本、删除）
- 点击复制功能

#### ColorCardRowView
色卡行视图组件：
- 横向滚动容器
- 色卡列表显示
- 添加按钮（仅原始行）
- 滚动同步处理

#### AddColorCardSheet
添加色卡弹窗：
- 背景色输入框
- 文字色输入框（默认白色）
- 颜色格式验证
- 确认/取消按钮

### 服务层

#### ColorBlindnessService
色盲模拟服务：
```swift
class ColorBlindnessService {
    static let shared = ColorBlindnessService()
    
    func applyProtanopia(to color: Color) -> Color
    func applyDeuteranopia(to color: Color) -> Color
}
```

### 工具类

#### ColorConverter
颜色转换工具类，支持多种颜色格式的相互转换：
```swift
class ColorConverter {
    static let shared = ColorConverter()
    
    // MARK: - 字符串转颜色
    func stringToColor(_ input: String) -> Color?
    func hexToColor(_ hex: String) -> Color?
    func rgbToColor(_ rgb: String) -> Color?
    func rgbaToColor(_ rgba: String) -> Color?
    func hslToColor(_ hsl: String) -> Color?
    
    // MARK: - 颜色转字符串
    func colorToHex(_ color: Color, includeAlpha: Bool = false) -> String
    func colorToRGB(_ color: Color) -> String
    func colorToRGBA(_ color: Color) -> String
    func colorToHSL(_ color: Color) -> String
    
    // MARK: - 验证方法
    func validateColorString(_ input: String) -> Bool
    func detectColorFormat(_ input: String) -> ColorFormat?
    
    // MARK: - 辅助方法
    private func normalizeHexString(_ hex: String) -> String
    private func parseRGBComponents(_ rgb: String) -> (r: Double, g: Double, b: Double)?
    private func parseRGBAComponents(_ rgba: String) -> (r: Double, g: Double, b: Double, a: Double)?
    private func parseHSLComponents(_ hsl: String) -> (h: Double, s: Double, l: Double)?
}

enum ColorFormat {
    case hex, rgb, rgba, hsl
}
```

## 数据模型

### 色卡数据结构

```swift
struct ColorCardData {
    let id: UUID
    let backgroundColor: Color
    let textColor: Color
    let hexValue: String
    let createdAt: Date
}
```

### 滚动状态管理

```swift
struct ScrollState {
    var position: CGFloat = 0
    var isScrolling: Bool = false
    var lastUpdateTime: Date = Date()
}
```

## 错误处理

### 错误类型定义

```swift
enum ColorBlindnessError: LocalizedError {
    case invalidHexFormat
    case emptyBackgroundColor
    case shaderLoadingFailed
    case colorConversionFailed
    
    var errorDescription: String? { get }
    var recoverySuggestion: String? { get }
}
```

### 错误处理策略

1. **输入验证错误**: 显示内联错误提示，阻止无效操作
2. **着色器错误**: 降级到默认颜色显示，记录错误日志
3. **系统错误**: 显示用户友好的错误对话框
4. **网络错误**: 提供重试机制和离线模式

## 测试策略

### 单元测试

1. **模型测试**
   - 色卡数据模型的创建和属性验证
   - 颜色格式转换的准确性
   - 十六进制颜色验证逻辑

2. **视图模型测试**
   - 色卡添加/删除逻辑
   - 滚动同步机制
   - 错误状态处理

3. **服务测试**
   - 色盲模拟算法准确性
   - 颜色转换功能
   - 着色器集成

### 集成测试

1. **界面导航测试**
   - 从主界面到色盲模拟器的导航
   - 返回导航的正确性
   - 深层链接支持

2. **用户交互测试**
   - 色卡操作流程
   - 弹窗显示和关闭
   - 滚动同步效果

3. **着色器集成测试**
   - Metal 着色器的正确加载
   - 色盲模拟效果的准确性
   - 性能表现测试

### UI 测试

1. **可访问性测试**
   - VoiceOver 支持
   - 动态字体支持
   - 高对比度模式兼容性

2. **响应式布局测试**
   - 不同屏幕尺寸适配
   - 横竖屏切换
   - 安全区域处理

3. **用户体验测试**
   - 交互反馈及时性
   - 动画流畅性
   - 错误提示清晰性

## 技术实现细节

### Metal 着色器集成

使用现有的 `Protanopia.metal` 和 `Deuteranopia.metal` 着色器：

```swift
// 应用红色盲效果
Text("Hi")
    .foregroundColor(textColor)
    .colorEffect(
        Shader(
            function: ShaderLibrary.default.protanopia,
            arguments: []
        )
    )
```

### 滚动同步机制

使用 `ScrollViewReader` 和 `@State` 实现三个滚动视图的同步：

```swift
@State private var scrollPosition: CGFloat = 0

ScrollViewReader { proxy in
    ScrollView(.horizontal) {
        // 色卡内容
    }
    .onScrollGeometryChange(for: CGFloat.self) { geometry in
        geometry.contentOffset.x
    } action: { oldValue, newValue in
        if abs(newValue - scrollPosition) > 1 {
            scrollPosition = newValue
            // 同步其他滚动视图
        }
    }
}
```

### 颜色格式处理

使用 `ColorConverter` 工具类支持多种颜色输入格式：

```swift
// 支持的颜色格式示例
let hexFormats = [
    "#FF0000",      // 标准十六进制
    "FF0000",       // 无#号十六进制
    "#FFFF0000",    // 带透明度的十六进制 (AARRGGBB)
    "#F00",         // 短格式十六进制
    "#FF00"         // 短格式带透明度
]

let rgbFormats = [
    "rgb(255, 0, 0)",           // 标准RGB
    "RGB(255,0,0)",             // 大写，无空格
    "rgb(100%, 0%, 0%)",        // 百分比格式
]

let rgbaFormats = [
    "rgba(255, 0, 0, 1.0)",     // 标准RGBA
    "rgba(255, 0, 0, 0.5)",     // 半透明
]

let hslFormats = [
    "hsl(0, 100%, 50%)",        // 标准HSL
    "HSL(0,100%,50%)",          // 大写，无空格
]

// 使用ColorConverter进行转换
let color = ColorConverter.shared.stringToColor(userInput)
```

#### 容错处理

ColorConverter 提供强大的容错能力：

1. **大小写不敏感**: 支持 `#FF0000` 和 `#ff0000`
2. **空格容忍**: 自动去除多余空格
3. **格式检测**: 自动识别颜色格式类型
4. **短格式支持**: `#F00` 自动扩展为 `#FF0000`
5. **透明度处理**: 支持 3、4、6、8 位十六进制格式
6. **百分比支持**: RGB 和 HSL 的百分比表示法

### 国际化支持

所有用户界面文本使用 `LocalizedStringKey`：

```swift
// 新增本地化字符串
"color_blindness_simulator" = "Color Blindness";
"red_blind_protanopia" = "Red-Blind (Protanopia)";
"green_blind_deuteranopia" = "Green-Blind (Deuteranopia)";
"add_color_card" = "Add Color Card";
"background_color" = "Background Color";
"text_color" = "Text Color";
"copy_success" = "Color copied to clipboard";
"invalid_color_format" = "Invalid color format. Please use RRGGBB format.";
"background_color_required" = "Background color is required.";
```

### 性能优化

1. **懒加载**: 使用 `LazyHStack` 优化大量色卡的渲染
2. **视图复用**: 色卡视图组件的高效复用
3. **着色器缓存**: Metal 着色器的编译结果缓存
4. **内存管理**: 及时释放不需要的颜色数据

